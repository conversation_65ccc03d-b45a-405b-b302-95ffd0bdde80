package svc

import (
	v1 "ai-gateway/api/svc/v1"
	"ai-gateway/internal/consts"
	"ai-gateway/internal/model"
	"ai-gateway/internal/service"
	"ai-gateway/utility"
	"context"
	"fmt"
	"net/http"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
)

/*
Svc
Input Parameters:
- ctx: context.Context
- _ *v1.SvcReq
Output:
- res *v1.SvcRes
- err error

This function handles a service request. It extracts information from the request, validates authorization, and sends data to a microservice. The response from the microservice is returned to the caller.
*/

func (c *ControlerV1) Svc(ctx context.Context, _ *v1.SvcReq) (res *v1.SvcRes, err error) {

	r := g.RequestFromCtx(ctx)
	uris := gstr.SplitAndTrim(r.RequestURI, "/")
	svc, action := "", ""
	if len(uris) > 2 {

		action = uris[len(uris)-1]
		if gstr.HasSuffix(gstr.ToLower(uris[1]), ".svc") {
			svc = gstr.TrimRightStr(gstr.ToLower(uris[1]), ".svc")
		} else {
			svc = uris[1]
		}
	}

	out, err := service.SVC().ReplaceToInstanceUri(
		ctx,
		model.ParseURIInput{
			RequestURI: gstr.Join(uris[1:], "/"),
		},
	)

	if err != nil {
		resp := utility.ParseRes(err)
		r.Response.WriteJson(resp)
		return
	}

	g.Log().Cat(consts.DEBUG).Debugf(ctx, "The service uri:%v url:%v",
		out.URI, out.URL,
	)

	// 先做验证权限
	if out.Authorized {
		tenantId := ""
		if gjson.Valid(r.GetBodyString()) {
			tenantId = gjson.New(r.GetBodyString()).Get(consts.HeaderTenantID, "").String()
		}

		result := false
		result, err = service.SVC().ValidateTenant(

			ctx,
			model.ValidateTenantInput{
				TenantID: fmt.Sprintf("%s^%s", r.GetHeader(consts.HeaderTenantID), tenantId),
				AppKey:   r.GetHeader(consts.HeaderAppKey),
				Svc:      svc,
				Token:    r.GetHeader(consts.HeaderAuth),
				Action:   action,
			},
		)

		if err != nil || !result {
			g.Log().Cat(consts.ERROR).Debug(ctx, "The current tenant does not have permission")
			r.Response.WriteStatus(http.StatusUnauthorized)
			return
		}
	}
	// 檢查tenant 並創建關聯的tables
	if out.Check {
		tenantId := ""
		if gjson.Valid(r.GetBodyString()) {
			tenantId = gjson.New(r.GetBodyString()).Get(consts.HeaderTenantID, "").String()
		}
		//  檢查tenant 是否存在 ，如果不存在則創建 並創建關聯表

	}

	// send to microservice

	c.client.SetHeaderMap(map[string]string{
		consts.HeaderAppKey:   r.GetHeader(consts.HeaderAppKey),
		consts.HeaderTenantID: r.GetHeader(consts.HeaderTenantID),
		consts.HeaderAuth:     r.GetHeader(consts.HeaderAuth),
	})

	var response *gclient.Response

	// Check for uploaded files
	if r.MultipartForm != nil && r.MultipartForm.File != nil && len(r.MultipartForm.File) > 0 {
		// Handle file uploads
		var tempFiles []string
		defer func() {
			// Clean up temp files
			for _, tempFile := range tempFiles {
				if gfile.Exists(tempFile) {
					_ = gfile.RemoveFile(tempFile)
				}
			}
		}()

		// Prepare form data
		formData := make(map[string]string)
		vParamsMap := r.GetFormMap()
		for k, v := range vParamsMap {
			formData[k] = fmt.Sprintf("%v", v)
		}

		// Process all uploaded files
		for fieldName, files := range r.MultipartForm.File {
			uploadFiles := r.GetUploadFiles(fieldName)
			if uploadFiles == nil {
				continue
			}

			for i, uploadFile := range uploadFiles {
				// Generate unique temp file path
				tempFilePath := gfile.Join(gfile.Temp(), uploadFile.Filename)
				if gfile.Exists(tempFilePath) {
					tempFilePath = gfile.Join(gfile.Temp(), fmt.Sprintf("%d_%s", gtime.TimestampMilli(), uploadFile.Filename))
				}

				// Save file to temp directory
				_, err = uploadFile.Save(gfile.Dir(tempFilePath))
				if err != nil {
					err = gerror.NewCode(consts.GeneralError, fmt.Sprintf("failed to save uploaded file: %v", err))
					r.Response.WriteJson(utility.ParseRes(err))
					return
				}

				tempFiles = append(tempFiles, tempFilePath)

				// Add file to form data
				if len(r.MultipartForm.File) == 1 && len(files) == 1 {
					// Single file upload - use the original field name
					formData["file"] = fmt.Sprintf("@file:%s", tempFilePath)
				} else {
					// Multiple files - use field name with index

					formData[fmt.Sprintf("file_%d", i)] = fmt.Sprintf("@file:%s", tempFilePath)
				}
			}
		}
		g.Dump(formData)
		// Build request data string
		var requestData string
		var uploadFileParams []string
		var otherParams []string
		for k, v := range formData {
			param := fmt.Sprintf("%s=%s", k, v)

			if gstr.HasPrefix(k, "file") {
				pos := gstr.PosRune(k, "_")
				if pos != -1 {
					k = gstr.SubStrRune(k, 0, pos)
					param = fmt.Sprintf("%s=%s", k, v)
				}
				uploadFileParams = append(uploadFileParams, param)
			} else {
				otherParams = append(otherParams, param)
			}

		}
		allParams := append(uploadFileParams, otherParams...)
		requestData = gstr.Join(allParams, "&")
		g.Log().Noticef(ctx, "data=%v", requestData)
		response, err = c.client.Post(ctx, out.URL, requestData)

	} else {
		// No file upload, forward request as is
		response, err = c.client.Post(ctx, out.URL, r.GetBody())
	}

	if err != nil {
		err = gerror.WrapCode(consts.PostRequestFailed, err, err.Error())
		resp := utility.ParseRes(err)
		r.Response.WriteJson(resp)
		return
	}

	defer func() { _ = response.Close() }()

	// 檢查響應是否為文件下載請求
	contentDisposition := response.Header.Get("Content-Disposition")
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Content-Disposition: %s", contentDisposition)
	isFileDownload := gstr.Contains(contentDisposition, "attachment") || gstr.Contains(contentDisposition, "inline")

	if isFileDownload {
		// 處理文件下載請求：轉發所有響應標頭和二進制內容
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Handling file download request with Content-Disposition: %s", contentDisposition)

		// 設置響應狀態碼
		r.Response.WriteStatus(response.StatusCode)

		// 轉發所有響應標頭到客戶端
		for headerName, headerValues := range response.Header {
			for _, headerValue := range headerValues {
				r.Response.Header().Add(headerName, headerValue)
			}
		}

		// 直接轉發二進制內容，不進行任何處理或轉換
		responseBody := response.ReadAll()
		// 從 Content-Disposition 標頭中提取文件名
		fileName := extractFileNameFromContentDisposition(contentDisposition)
		if fileName == "" {
			fileName = "download_file" // 默認文件名
		}

		// 創建臨時文件
		tempFilePath := gfile.Join(gfile.Temp(), fmt.Sprintf("%d_%s", gtime.TimestampMilli(), fileName))

		// 將響應內容寫入臨時文件
		err = gfile.PutBytes(tempFilePath, responseBody)
		if err != nil {
			g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to create temp file: %v", err)
			r.Response.WriteStatus(http.StatusInternalServerError)
			return
		}

		//確保在處理完成後清理臨時文件
		defer func() {
			if gfile.Exists(tempFilePath) {
				_ = gfile.RemoveFile(tempFilePath)
			}
		}()

		// 使用 ServeFileDownload 方法
		r.Response.ServeFileDownload(tempFilePath, fileName)

		g.Log().Cat(consts.DEBUG).Debugf(ctx, "File download response forwarded successfully, content length: %d bytes", len(responseBody))
		return
	}

	// 對於非文件下載請求，保持原有的處理邏輯
	r.Response.WriteJson(response.ReadAllString())

	return

}

// extractFileNameFromContentDisposition 從 Content-Disposition 標頭中提取文件名
func extractFileNameFromContentDisposition(contentDisposition string) string {
	if contentDisposition == "" {
		return ""
	}

	// 查找 filename= 參數
	if pos := gstr.Pos(contentDisposition, "filename="); pos != -1 {
		filename := gstr.SubStr(contentDisposition, pos+9) // "filename=" 長度為 9

		// 移除引號
		filename = gstr.Trim(filename, "\"")
		filename = gstr.Trim(filename, "'")

		// 如果有分號，只取分號前的部分
		if pos := gstr.Pos(filename, ";"); pos != -1 {
			filename = gstr.SubStr(filename, 0, pos)
		}

		return gstr.Trim(filename)
	}

	return ""
}

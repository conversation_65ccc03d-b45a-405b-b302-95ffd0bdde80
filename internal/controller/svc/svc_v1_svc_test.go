package svc

import (
	"ai-gateway/internal/consts"
	"ai-gateway/internal/model"
	"ai-gateway/internal/service"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/test/gtest"
	"net/http"
	"os"
	"testing"
)

// MockSVC is a mock implementation of the ISVC interface
type MockSVC struct {
	ReplaceToInstanceUriFn func(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error)
	ValidateTenantFn       func(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error)
}

func (m *MockSVC) ReplaceToInstanceUri(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error) {
	return m.ReplaceToInstanceUriFn(ctx, in)
}

func (m *MockSVC) ValidateTenant(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error) {
	return m.ValidateTenantFn(ctx, in)
}

// MockResponse implements the necessary methods for a gclient.Response
type MockResponse struct {
	*http.Response
	Body string
}

func (m *MockResponse) ReadAllString() string {
	return m.Body
}

func (m *MockResponse) Close() error {
	return nil
}

// TestSvc tests the Svc method of ControlerV1
func TestSvc(t *testing.T) {
	// 初始化一個默認的mock服務，避免panic
	defaultMockSVC := &MockSVC{
		ReplaceToInstanceUriFn: func(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error) {
			return &model.ParseURIOutput{}, nil
		},
		ValidateTenantFn: func(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error) {
			return true, nil
		},
	}

	// 註冊默認服務以避免初始化時的panic
	service.RegisterSVC(defaultMockSVC)

	// Save original service implementation (現在不會panic)
	originalSVC := service.SVC()

	// Test case for basic request
	gtest.C(t, func(t *gtest.T) {
		// Create a mock SVC
		mockSVC := &MockSVC{
			ReplaceToInstanceUriFn: func(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error) {
				return &model.ParseURIOutput{
					URI:        "test/service",
					URL:        "http://test-service/api",
					Authorized: false,
				}, nil
			},
			ValidateTenantFn: func(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error) {
				return true, nil
			},
		}

		// Register mock service
		service.RegisterSVC(mockSVC)

		// Create a mock HTTP client
		client := g.Client()

		// Create a controller with the client
		controller := &ControlerV1{
			client: client,
		}

		// Create a test server
		s := ghttp.GetServer()

		// Bind the controller to the server using group.Bind()
		s.Group("/", func(group *ghttp.RouterGroup) {
			group.Bind(controller)
		})

		s.SetDumpRouterMap(false)
		s.Start()
		defer s.Shutdown()

		// Make a request to the test server
		client.SetPrefix(fmt.Sprintf("http://127.0.0.1:%d", s.GetListenedPort()))
		resp, err := client.Post(context.Background(), "/svc/test/service", `{"key":"value"}`)
		t.Assert(err, nil)
		defer resp.Close()
		t.Assert(resp.StatusCode, http.StatusOK)
	})

	// Test case for authorization required
	gtest.C(t, func(t *gtest.T) {
		// Create a mock SVC
		mockSVC := &MockSVC{
			ReplaceToInstanceUriFn: func(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error) {
				return &model.ParseURIOutput{
					URI:        "test/service",
					URL:        "http://test-service/api",
					Authorized: true,
				}, nil
			},
			ValidateTenantFn: func(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error) {
				// Validate that the correct headers are passed
				t.Assert(in.AppKey, "test-app-key")
				t.Assert(in.Token, "test-token")
				return true, nil
			},
		}

		// Register mock service
		service.RegisterSVC(mockSVC)

		// Create a mock HTTP client
		client := g.Client()

		// Create a controller with the client
		controller := &ControlerV1{
			client: client,
		}

		// Create a test server
		s := ghttp.GetServer()

		// Bind the controller to the server using group.Bind()
		s.Group("/", func(group *ghttp.RouterGroup) {
			group.Bind(controller)
		})

		s.SetDumpRouterMap(false)
		s.Start()
		defer s.Shutdown()

		// Make a request to the test server
		client.SetPrefix(fmt.Sprintf("http://127.0.0.1:%d", s.GetListenedPort()))

		// Set required headers
		client.SetHeader(consts.HeaderAppKey, "test-app-key")
		client.SetHeader(consts.HeaderTenantID, "test-tenant")
		client.SetHeader(consts.HeaderAuth, "test-token")
		resp, err := client.Post(context.Background(), "/svc/test/service", `{"key":"value"}`)
		t.Assert(err, nil)
		defer resp.Close()
		t.Assert(resp.StatusCode, http.StatusOK)
	})

	// Test case for file upload
	gtest.C(t, func(t *gtest.T) {
		// Create a mock SVC
		mockSVC := &MockSVC{
			ReplaceToInstanceUriFn: func(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error) {
				return &model.ParseURIOutput{
					URI:        "test/upload",
					URL:        "http://test-service/upload",
					Authorized: false,
				}, nil
			},
			ValidateTenantFn: func(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error) {
				return true, nil
			},
		}

		// Register mock service
		service.RegisterSVC(mockSVC)

		// Create a mock HTTP client
		client := g.Client()

		// Create a controller with the client
		controller := &ControlerV1{
			client: client,
		}

		// Create a temporary file for testing
		tempFile, err := os.CreateTemp("", "test-upload-*.txt")
		t.Assert(err, nil)
		defer os.Remove(tempFile.Name())
		_, err = tempFile.WriteString("test file content")
		t.Assert(err, nil)
		tempFile.Close()

		// Create a test server
		s := ghttp.GetServer()

		// Bind the controller to the server using group.Bind()
		s.Group("/", func(group *ghttp.RouterGroup) {
			group.Bind(controller)
		})

		s.SetDumpRouterMap(false)
		s.Start()
		defer s.Shutdown()

		// Make a request to the test server with file upload
		client.SetPrefix(fmt.Sprintf("http://127.0.0.1:%d", s.GetListenedPort()))

		// Create form data with file
		formData := make(g.Map)
		formData["file"] = "@file:" + tempFile.Name()

		resp, err := client.Post(context.Background(), "/svc/test/upload", formData)
		t.Assert(err, nil)
		defer resp.Close()
		t.Assert(resp.StatusCode, http.StatusOK)
	})

	// Test case for error handling
	gtest.C(t, func(t *gtest.T) {
		// Create a mock SVC with error
		mockSVC := &MockSVC{
			ReplaceToInstanceUriFn: func(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error) {
				return nil, fmt.Errorf("service error")
			},
			ValidateTenantFn: func(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error) {
				return false, nil
			},
		}

		// Register mock service
		service.RegisterSVC(mockSVC)

		// Create a mock HTTP client
		client := g.Client()

		// Create a controller with the client
		controller := &ControlerV1{
			client: client,
		}

		// Create a test server
		s := ghttp.GetServer()

		// Bind the controller to the server using group.Bind()
		s.Group("/", func(group *ghttp.RouterGroup) {
			group.Bind(controller)
		})

		s.SetDumpRouterMap(false)
		s.Start()
		defer s.Shutdown()

		// Make a request to the test server
		client.SetPrefix(fmt.Sprintf("http://127.0.0.1:%d", s.GetListenedPort()))
		resp, err := client.Post(context.Background(), "/svc/test/error", `{"key":"value"}`)
		t.Assert(err, nil)
		defer resp.Close()
	})

	// Restore original service
	if originalSVC != nil {
		service.RegisterSVC(originalSVC)
	}
}

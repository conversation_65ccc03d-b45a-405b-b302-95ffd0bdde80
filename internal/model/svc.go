package model

import (
	"ai-gateway/internal/consts"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type ValidateTenantInput struct {
	TenantID string `json:"tenant_id"`
	A<PERSON><PERSON>ey   string `json:"app_key"`
	Svc      string `json:"svc"`
	Token    string `json:"token"`
	Action   string `json:"action"`
}
type ParseURIInput struct {
	RequestURI string `json:"request_uri"`
}
type ParseURIOutput struct {
	URI        string `json:"uri"`
	URL        string `json:"url"`
	Authorized bool   `json:"authorized"`
	Check      bool   `json:"check"`
}
type ServicesUri struct {
	Id         string `json:"id"`         // service name
	Path       string `json:"path"`       // gateway request uri
	Uri        string `json:"uri"`        // micro-service uri
	Scheme     string `json:"schema"`     // http or https optional (default is http)ser
	Authorized bool   `json:"authorized"` // Is authentication required? Default is true.
	Service    string `json:"service"`    // microservice name
	Check      bool   `json:"check"`      // check tenant and create some tables
}

func (s *ServicesUri) ParseUri(sPath, sNest string) (err error, newOne string) {
	// sample -  sPath /abc/*  s.Path is /abc/*  then they are same
	if s.Path != sPath || g.IsEmpty(s.Uri) {
		err = gerror.NewCode(consts.BadRequestUri)
		return
	}

	if s.Uri[len(s.Uri)-1:] != `/` {
		s.Uri += `/`
	}

	newOne = s.Uri + sNest
	return
}
